import React from 'react';
import RealTimeAudioWave from './RealTimeAudioWave';

// Example component showing different audio wave styles
function AudioWaveExamples() {
  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      gap: '20px', 
      padding: '20px',
      backgroundColor: '#1a1a1a',
      borderRadius: '8px'
    }}>
      <h3 style={{ color: 'white', margin: 0 }}>Audio Wave Styles</h3>
      
      <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
        <span style={{ color: 'white', minWidth: '80px' }}>Bars:</span>
        <RealTimeAudioWave 
          width={60}
          height={24}
          color="#0a84ff"
          sensitivity={2.0}
          style="bars"
          barCount={7}
        />
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
        <span style={{ color: 'white', minWidth: '80px' }}>Wave:</span>
        <RealTimeAudioWave 
          width={60}
          height={24}
          color="#00ff88"
          sensitivity={2.0}
          style="wave"
        />
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
        <span style={{ color: 'white', minWidth: '80px' }}>Circle:</span>
        <RealTimeAudioWave 
          width={60}
          height={24}
          color="#ff6b6b"
          sensitivity={2.0}
          style="circle"
        />
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
        <span style={{ color: 'white', minWidth: '80px' }}>Line:</span>
        <RealTimeAudioWave 
          width={60}
          height={24}
          color="#ffd93d"
          sensitivity={2.0}
          style="line"
        />
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
        <span style={{ color: 'white', minWidth: '80px' }}>Compact:</span>
        <RealTimeAudioWave 
          width={40}
          height={16}
          color="#a855f7"
          sensitivity={1.5}
          style="bars"
          barCount={4}
        />
      </div>
    </div>
  );
}

export default AudioWaveExamples;
