import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';

function RealTimeAudioWave({
  className = '',
  width = 60,
  height = 24,
  color = '#0a84ff',
  sensitivity = 2.0,
  style = 'wave', // 'wave', 'bars', 'circle', 'line'
  barCount = 7
}) {
  const [audioLevel, setAudioLevel] = useState(0);
  const [audioLevels, setAudioLevels] = useState(new Array(barCount).fill(0));
  const [isActive, setIsActive] = useState(false);
  const animationRef = useRef();
  const analyserRef = useRef();
  const dataArrayRef = useRef();
  const { localParticipant } = useLocalParticipant();

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioData = () => {
      if (!analyserRef.current || !dataArrayRef.current) return;

      analyserRef.current.getByteFrequencyData(dataArrayRef.current);
      
      // Calculate average volume for overall level
      const average = dataArrayRef.current.reduce((sum, value) => sum + value, 0) / dataArrayRef.current.length;
      const normalizedLevel = Math.min(average / 128 * sensitivity, 1);
      
      setAudioLevel(normalizedLevel);

      // For bar visualization, create individual bar levels
      if (style === 'bars') {
        const newLevels = new Array(barCount).fill(0).map((_, index) => {
          const freqIndex = Math.floor((index / barCount) * dataArrayRef.current.length);
          const freqLevel = dataArrayRef.current[freqIndex] / 255;
          const baseLevel = normalizedLevel * 0.7;
          const variation = (Math.random() - 0.5) * 0.2;
          return Math.max(0, Math.min(1, baseLevel + freqLevel * 0.3 + variation));
        });
        setAudioLevels(newLevels);
      }

      animationRef.current = requestAnimationFrame(updateAudioData);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant) return;

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack || !micTrack.mediaStream) return;

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        const bufferLength = analyserRef.current.frequencyBinCount;
        dataArrayRef.current = new Uint8Array(bufferLength);

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        setIsActive(true);
        updateAudioData();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setIsActive(false);
      }
    };

    if (localParticipant?.isMicrophoneEnabled) {
      setupAudioAnalysis();
    } else {
      setIsActive(false);
      setAudioLevel(0);
      setAudioLevels(new Array(barCount).fill(0));
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled, sensitivity, style, barCount]);

  const renderWaveStyle = () => {
    const waveHeight = Math.max(2, audioLevel * height);
    const opacity = isActive && audioLevel > 0.05 ? 1 : 0.3;
    
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative'
        }}
      >
        {/* Central wave line */}
        <div
          style={{
            width: '100%',
            height: '2px',
            backgroundColor: color,
            opacity: opacity * 0.5,
            position: 'absolute'
          }}
        />
        {/* Dynamic wave */}
        <div
          style={{
            width: `${Math.max(4, audioLevel * width)}px`,
            height: `${waveHeight}px`,
            backgroundColor: color,
            opacity: opacity,
            borderRadius: '2px',
            transition: 'all 0.1s ease-out'
          }}
        />
      </div>
    );
  };

  const renderBarsStyle = () => {
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          display: 'flex',
          alignItems: 'flex-end',
          justifyContent: 'center',
          gap: '1px'
        }}
      >
        {audioLevels.map((level, index) => (
          <div
            key={index}
            style={{
              width: `${Math.floor(width / barCount) - 1}px`,
              height: `${Math.max(2, level * height)}px`,
              backgroundColor: isActive && level > 0.05 ? color : '#666',
              borderRadius: '1px',
              transition: 'height 0.1s ease-out, background-color 0.2s ease',
              opacity: isActive ? 1 : 0.3
            }}
          />
        ))}
      </div>
    );
  };

  const renderCircleStyle = () => {
    const circleSize = Math.max(8, audioLevel * height);
    const opacity = isActive && audioLevel > 0.05 ? 1 : 0.3;
    
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <div
          style={{
            width: `${circleSize}px`,
            height: `${circleSize}px`,
            backgroundColor: color,
            borderRadius: '50%',
            opacity: opacity,
            transition: 'all 0.1s ease-out',
            boxShadow: isActive && audioLevel > 0.1 ? `0 0 ${audioLevel * 10}px ${color}` : 'none'
          }}
        />
      </div>
    );
  };

  const renderLineStyle = () => {
    const lineWidth = Math.max(2, audioLevel * width);
    const opacity = isActive && audioLevel > 0.05 ? 1 : 0.3;
    
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <div
          style={{
            width: `${lineWidth}px`,
            height: '3px',
            backgroundColor: color,
            opacity: opacity,
            borderRadius: '2px',
            transition: 'all 0.1s ease-out'
          }}
        />
      </div>
    );
  };

  const renderVisualization = () => {
    switch (style) {
      case 'bars':
        return renderBarsStyle();
      case 'circle':
        return renderCircleStyle();
      case 'line':
        return renderLineStyle();
      case 'wave':
      default:
        return renderWaveStyle();
    }
  };

  return (
    <div className={`real-time-audio-wave ${className}`}>
      {renderVisualization()}
    </div>
  );
}

export default RealTimeAudioWave;
